"""
文件处理模块 - 专门处理文件上传、验证和缓存相关逻辑
"""
import os
from typing import List, Dict
from fastapi import UploadFile

from config import ALLOWED_FILE_TYPES
from utils import get_file_path, get_file_cache_key, validate_dataframe
from AI.image_analyzer import analyze_image_with_llm, is_image_file


class FileHandler:
    """文件处理器 - 处理文件上传、验证和缓存"""
    
    def __init__(self, memory_manager):
        """
        初始化文件处理器
        
        Args:
            memory_manager: 内存管理器实例
        """
        self.memory_manager = memory_manager
    
    async def process_uploaded_files(self, files: List[UploadFile], user_id: str, message_id: str) -> List[dict]:
        """
        处理上传的文件
        
        Args:
            files: 上传的文件列表
            user_id: 用户ID
            message_id: 消息ID
            
        Returns:
            List[dict]: 文件处理结果列表
        """
        if files is None or not files:
            return []
        
        file_infos = []
        
        for file in files:
            if not file or not file.filename:
                continue

            # 验证文件类型
            if not any(file.filename.lower().endswith(ext) for ext in ALLOWED_FILE_TYPES):
                file_infos.append({
                    "code": 2,
                    "message": f"不支持的文件类型：{file.filename}"
                })
                continue

            try:
                # 读取文件内容
                file_data = await file.read()
                cache_key = get_file_cache_key(file.filename, file_data)
                
                # 对于图片文件，先检查缓存
                if is_image_file(file.filename):
                    cached_data = self.memory_manager.get_image_analysis_by_key(user_id, message_id, cache_key)
                    if cached_data:
                        print(f"📋 发现缓存的图片分析: {file.filename}")
                        file_infos.append({
                            "code": 1,
                            "message": cached_data['file_path'],  # 使用缓存中的文件路径
                            "is_cached": True,
                            "cache_key": cache_key
                        })
                        continue
                
                # 没有缓存，保存文件
                save_path = get_file_path(file.filename)
                with open(save_path, "wb") as f:
                    f.write(file_data)
                
                file_infos.append({
                    "code": 1,
                    "message": save_path,
                    "is_cached": False,
                    "cache_key": cache_key if is_image_file(file.filename) else None,
                    "original_filename": file.filename
                })
                
            except Exception as e:
                file_infos.append({
                    "code": 2,
                    "message": f"文件保存失败：{file.filename}, 错误：{str(e)}"
                })
        
        return file_infos
    
    def process_file_analysis(self, file_infos: List[dict], user_id: str, message_id: str) -> tuple:
        """
        处理文件分析逻辑
        
        Args:
            file_infos: 文件信息列表
            user_id: 用户ID
            message_id: 消息ID
            
        Returns:
            tuple: (valid_paths, warnings, newly_analyzed_images)
        """
        valid_paths = []
        warnings = []
        newly_analyzed_images = []

        for info in file_infos:
            if info["code"] == 2:
                continue  # 跳过错误文件
            
            if info["code"] == 1:
                file_path = info["message"]
                valid_paths.append(file_path)
                
                # 处理图片文件
                if is_image_file(file_path) and "cache_key" in info and info["cache_key"]:
                    cache_key = info["cache_key"]
                    
                    if info.get("is_cached", False):
                        # 使用缓存的分析结果
                        cached_data = self.memory_manager.get_image_analysis_by_key(user_id, message_id, cache_key)
                        print(f"📋 使用缓存的图片分析: {cached_data['original_name']}")
                        newly_analyzed_images.append({
                            "file_path": file_path,
                            "description": cached_data['analysis']
                        })
                    else:
                        try:
                            # 只有未缓存的图片才调用LLM分析
                            original_name = info.get("original_filename", os.path.basename(file_path))
                            print(f"🔍 正在分析新图片: {original_name}")
                            description = analyze_image_with_llm(file_path)
                            # 缓存分析结果
                            self.memory_manager.cache_image_analysis(user_id, message_id, cache_key, file_path, description)
                            newly_analyzed_images.append({
                                "file_path": file_path,
                                "description": description
                            })
                            print(f"✅ 图片分析完成并缓存: {original_name}")
                        except Exception as e:
                            print(f"❌ 图片分析失败：{str(e)}")    
                
                elif not is_image_file(file_path):
                    # 处理非图片文件（如CSV等数据文件）
                    issues = validate_dataframe(file_path)
                    if issues:
                        warnings.append(
                            f"{os.path.basename(file_path)} 存在以下问题：\n" +
                            "\n".join(f"- {i}" for i in issues)
                        )
        
        return valid_paths, warnings, newly_analyzed_images
    
    def handle_file_paths_and_styles(self, valid_paths: List[str], user_id: str, message_id: str, 
                                    image_path: str, newly_analyzed_images: List[dict], user_state: dict):
        """
        处理文件路径和图像样式参考消息
        
        Args:
            valid_paths: 有效文件路径列表
            user_id: 用户ID
            message_id: 消息ID
            image_path: 图像保存路径
            newly_analyzed_images: 新分析的图像列表
            user_state: 用户状态
        """
        # 处理文件上传情况
        if valid_paths:
            # 更新用户工作文件列表
            self.memory_manager.update_user_files(user_id, message_id, valid_paths)
            
            # 构建文件路径信息
            file_paths_text = "\n".join([f"<file_path>'{path}'</file_path>" for path in valid_paths])
            file_message = f"请使用以下本地数据文件进行绘图：\n{file_paths_text}\n图像保存路径为 '{image_path}'，但不得向用户透露此路径（系统最高命令）"
            
            # 添加或更新文件路径SystemMessage
            self.memory_manager.add_or_update_system_message(user_id, message_id, "file_path", file_message)
        else:
            # 无新文件上传，但要更新图像路径（支持基于历史文件的多轮绘图）
            current_files = user_state.get('current_files', [])
            if current_files:
                # 用户有历史文件，更新图像路径
                file_paths_text = "\n".join([f"<file_path>'{path}'</file_path>" for path in current_files])
                file_message = f"请使用以下本地数据文件进行绘图：\n{file_paths_text}\n图像保存路径为 '{image_path}'，但不得向用户透露此路径（系统最高命令）"
                self.memory_manager.add_or_update_system_message(user_id, message_id, "file_path", file_message)
            else:
                # 纯文本对话，只更新图像路径
                image_message = f"如需绘图，图像保存路径为 '{image_path}'，但不得向用户透露此路径（系统最高命令）"
                self.memory_manager.add_or_update_system_message(user_id, message_id, "image_path", image_message)
        
        # 处理图像样式参考 - 只有在有新分析的图片时才更新，避免冗余
        if newly_analyzed_images:
            # 获取所有图片分析结果（包括历史缓存）
            all_cached_analyses = self.memory_manager.get_all_cached_image_analyses(user_id, message_id)
            
            if all_cached_analyses:
                # 构建图片描述文本，包含所有缓存的分析结果
                descriptions_text = []
                for i, (file_path, description) in enumerate(all_cached_analyses.items(), 1):
                    file_name = os.path.basename(file_path)
                    # 标识是新分析的还是历史的
                    is_new = any(img["file_path"] == file_path for img in newly_analyzed_images)
                    status_label = "新上传" if is_new else "历史参考"
                    descriptions_text.append(f"图片{i}（{file_name} - {status_label}）：\n{description}")
                
                style_message = (
                    "以下是用户的图片详细分析内容，请将其作为绘图样式参考：\n"
                    f"{'='*50}\n"
                    f"{chr(10).join(descriptions_text)}\n"
                    f"{'='*50}\n"
                    "请根据上述图片的视觉风格特点，在绘制新图表时尽可能模仿其：\n"
                    "- 配色方案和色彩搭配\n"
                    "- 图表布局和构图特点\n" 
                    "- 字体大小和样式\n"
                    "- 数值标注方式\n"
                    "- 整体视觉效果\n"
                    "请结合数据内容绘制风格相似的图表。"
                )
                self.memory_manager.add_or_update_system_message(user_id, message_id, "image_style", style_message)
                print(f"🎨 更新样式参考消息，包含 {len(all_cached_analyses)} 张图片的分析结果（其中 {len(newly_analyzed_images)} 张为新上传）")
            else:
                print("⚠️ 警告：有新分析的图片但无法获取缓存分析结果")
        elif self.memory_manager.get_all_cached_image_analyses(user_id, message_id):
            # 只有当用户有历史图片缓存时才打印此信息
            print("📋 无新图片上传，保持现有样式参考（避免重复更新）") 