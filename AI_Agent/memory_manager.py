"""
内存管理模块 - 包含用户对话内存和状态管理
"""
import os
import time
from typing import List, Dict
from collections import OrderedDict
from threading import Lock

from langchain.memory import ConversationBufferMemory
from langchain.schema import AIMessage, HumanMessage
from langchain_core.messages import SystemMessage

from database import ChatDatabase
from config import DEFAULT_MAX_CACHE_SIZE, DEFAULT_CACHE_TTL

class UserMemoryManager:
    """用户内存管理器 - 管理用户对话记忆和状态"""
    
    def __init__(self, max_cache_size: int = DEFAULT_MAX_CACHE_SIZE, cache_ttl: int = DEFAULT_CACHE_TTL):
        # 使用 OrderedDict 实现 LRU 缓存
        self.user_memories = OrderedDict()
        self.user_states = OrderedDict()
        self.max_cache_size = max_cache_size  # 最大缓存数量
        self.cache_ttl = cache_ttl  # 缓存过期时间(秒)
        self.access_times = {}  # 记录访问时间
        self.lock = Lock()  # 线程锁
        self.db = ChatDatabase()  # 数据库实例
        
        print(f"💾 UserMemoryManager 初始化: 最大缓存数={max_cache_size}, TTL={cache_ttl}秒")
    
    def _cleanup_expired_cache(self):
        """清理过期的缓存条目"""
        current_time = time.time()
        expired_keys = []
        
        for key, access_time in self.access_times.items():
            if current_time - access_time > self.cache_ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            self._remove_cache_entry(key)
            print(f"🗑️ 清理过期缓存: {key}")
    
    def _remove_cache_entry(self, memory_key: str):
        """移除指定的缓存条目"""
        self.user_memories.pop(memory_key, None)
        self.user_states.pop(memory_key, None)
        self.access_times.pop(memory_key, None)
    
    def _filter_duplicate_code_messages(self, messages: List[dict]) -> List[dict]:
        """
        过滤重复的绘图代码系统消息，只保留最新的一个
        
        Args:
            messages: 原始消息列表
            
        Returns:
            List[dict]: 过滤后的消息列表
        """
        filtered_messages = []
        latest_code_message = None
        latest_code_index = -1
        
        # 找到最新的绘图代码消息
        for i, msg in enumerate(messages):
            if (msg["role"] == "system" and 
                msg["content"] and 
                "上次生成的绘图代码" in msg["content"]):
                latest_code_message = msg
                latest_code_index = i
        
        # 过滤消息：移除旧的绘图代码消息，保留最新的
        for i, msg in enumerate(messages):
            if (msg["role"] == "system" and 
                msg["content"] and 
                "上次生成的绘图代码" in msg["content"]):
                # 只保留最新的绘图代码消息
                if i == latest_code_index:
                    filtered_messages.append(msg)
                # 跳过其他旧的绘图代码消息
            else:
                # 保留非绘图代码的所有消息
                filtered_messages.append(msg)
        
        if latest_code_message:
            print(f"🔧 过滤绘图代码消息: 保留最新的，移除了 {sum(1 for msg in messages if msg['role'] == 'system' and '上次生成的绘图代码' in msg.get('content', '')) - 1} 个旧消息")
        
        return filtered_messages
    
    def _restore_plotting_state_from_db(self, user_id: str, message_id: str, state_key: str):
        """
        从数据库的系统消息中恢复绘图代码状态
        
        Args:
            user_id: 用户ID
            message_id: 消息ID
            state_key: 状态键
        """
        try:
            # 从数据库加载所有消息
            db_messages = self.db.load_messages(user_id, message_id)
            
            # 查找最新的绘图代码消息
            latest_code_message = None
            for msg in reversed(db_messages):  # 从最新的开始查找
                if (msg["role"] == "system" and 
                    msg["content"] and 
                    "上次生成的绘图代码" in msg["content"]):
                    latest_code_message = msg
                    break
            
            if latest_code_message:
                # 从系统消息中提取代码
                content = latest_code_message["content"]
                
                # 提取代码次数
                import re
                count_match = re.search(r'第(\d+)次', content)
                code_count = int(count_match.group(1)) if count_match else 1
                
                # 提取代码内容（在```python和```之间）
                code_match = re.search(r'```python\n(.*?)\n```', content, re.DOTALL)
                if code_match:
                    plotting_code = code_match.group(1)
                    
                    # 恢复到用户状态
                    state = self.user_states[state_key]
                    state['latest_plotting_code'] = plotting_code
                    state['code_generation_count'] = code_count
                    
                    print(f"💾 从数据库恢复绘图代码状态: 第{code_count}次, 代码长度: {len(plotting_code)} 字符")
                else:
                    print("⚠️ 无法从系统消息中提取绘图代码")
            else:
                print(f"ℹ️ 数据库中未找到绘图代码历史记录: {user_id}:{message_id}")
                
        except Exception as e:
            print(f"❌ 恢复绘图代码状态失败: {str(e)}")
    
    def _enforce_cache_size_limit(self):
        """强制执行缓存大小限制（LRU策略）"""
        while len(self.user_memories) > self.max_cache_size:
            # 移除最久未使用的条目（OrderedDict 的第一个）
            oldest_key = next(iter(self.user_memories))
            self._remove_cache_entry(oldest_key)
            print(f"📦 LRU清理: {oldest_key} (缓存已满)")
    
    def _update_access_time(self, memory_key: str):
        """更新访问时间并移到OrderedDict末尾（最近使用）"""
        self.access_times[memory_key] = time.time()
        
        # 移到OrderedDict末尾表示最近使用
        if memory_key in self.user_memories:
            self.user_memories.move_to_end(memory_key)
        if memory_key in self.user_states:
            self.user_states.move_to_end(memory_key)
    
    def get_cache_stats(self) -> dict:
        """获取缓存统计信息"""
        return {
            "cached_conversations": len(self.user_memories),
            "cached_states": len(self.user_states), 
            "max_cache_size": self.max_cache_size,
            "cache_ttl": self.cache_ttl
        }
    
    def get_memory(self, user_id: str, message_id: str) -> ConversationBufferMemory:
        """获取指定用户和对话ID的内存，支持从数据库加载历史记录"""
        memory_key = f"{user_id}:{message_id}"
        
        with self.lock:
            # 清理过期缓存
            self._cleanup_expired_cache()
            
            if memory_key not in self.user_memories:
                # 创建新的内存实例
                memory = ConversationBufferMemory(output_key="output")
                
                # 从数据库加载历史消息
                messages = self.db.load_messages(user_id, message_id)
                
                # 过滤历史消息，确保只保留最新的绘图代码系统消息
                filtered_messages = self._filter_duplicate_code_messages(messages)
                
                for msg in filtered_messages:
                    if msg["role"] == "human":
                        memory.chat_memory.add_message(HumanMessage(content=msg["content"]))
                    elif msg["role"] == "ai":
                        memory.chat_memory.add_message(AIMessage(content=msg["content"]))
                    elif msg["role"] == "system":
                        if msg["file_ids"]:
                            memory.chat_memory.add_message(SystemMessage(content=msg["content"], file_ids=msg["file_ids"]))
                        else:
                            memory.chat_memory.add_message(SystemMessage(content=msg["content"]))
                
                # 添加到缓存
                self.user_memories[memory_key] = memory
                
                # 强制执行缓存大小限制
                self._enforce_cache_size_limit()
                
                print(f"📥 从数据库加载对话到缓存: {memory_key} (原始消息数: {len(messages)}, 过滤后: {len(filtered_messages)})")
            
            # 更新访问时间
            self._update_access_time(memory_key)
            
            return self.user_memories[memory_key]
    
    def save_message_to_db(self, user_id: str, message_id: str, role: str, content: str, file_ids: List[str] = None, image_path: str = None):
        """保存消息到数据库"""
        self.db.save_message(user_id, message_id, role, content, file_ids, image_path)
    
    def get_user_state(self, user_id: str, message_id: str) -> dict:
        """获取用户状态"""
        state_key = f"{user_id}:{message_id}"
        
        with self.lock:
            # 清理过期缓存
            self._cleanup_expired_cache()
            
            if state_key not in self.user_states:
                self.user_states[state_key] = {
                    'current_files': [],  # 当前工作文件列表
                    'current_image_path': None,  # 当前图像路径
                    'latest_plotting_code': None,  # 最新生成的绘图代码
                    'code_generation_count': 0,  # 代码生成次数计数器
                    'image_analysis_cache': {}  # 图片分析结果缓存 {file_path: analysis_result}
                }
                
                # 尝试从数据库恢复绘图代码状态
                self._restore_plotting_state_from_db(user_id, message_id, state_key)
                
                # 强制执行缓存大小限制
                self._enforce_cache_size_limit()
                
                print(f"🆕 创建新用户状态: {state_key}")
            
            # 更新访问时间
            self._update_access_time(state_key)
            
            return self.user_states[state_key]
    
    def update_user_files(self, user_id: str, message_id: str, file_paths: List[str]):
        """更新用户工作文件"""
        state = self.get_user_state(user_id, message_id)
        state['current_files'] = file_paths
    
    def update_image_path(self, user_id: str, message_id: str, image_path: str):
        """更新用户图像路径"""
        state = self.get_user_state(user_id, message_id)
        state['current_image_path'] = image_path
    
    def cache_image_analysis(self, user_id: str, message_id: str, cache_key: str, file_path: str, analysis_result: str):
        """缓存图片分析结果 - 使用内容哈希作为key"""
        state = self.get_user_state(user_id, message_id)
        # 存储分析结果和对应的文件路径
        state['image_analysis_cache'][cache_key] = {
            'analysis': analysis_result,
            'file_path': file_path,
            'original_name': os.path.basename(file_path).split('_', 3)[-1] if '_' in os.path.basename(file_path) else os.path.basename(file_path)
        }
        print(f"📸 缓存图片分析结果: {state['image_analysis_cache'][cache_key]['original_name']} -> {len(analysis_result)} 字符")
    
    def get_image_analysis_by_key(self, user_id: str, message_id: str, cache_key: str) -> dict:
        """根据缓存key获取图片分析结果"""
        state = self.get_user_state(user_id, message_id)
        return state['image_analysis_cache'].get(cache_key, None)
    
    def get_image_analysis(self, user_id: str, message_id: str, file_path: str) -> str:
        """获取缓存的图片分析结果（兼容旧接口）"""
        state = self.get_user_state(user_id, message_id)
        cached_data = state['image_analysis_cache'].get(file_path, None)
        if isinstance(cached_data, dict):
            return cached_data.get('analysis', None)
        return cached_data  # 兼容旧格式
    
    def get_all_cached_image_analyses(self, user_id: str, message_id: str) -> dict:
        """获取所有缓存的图片分析结果"""
        state = self.get_user_state(user_id, message_id)
        result = {}
        for cache_key, cached_data in state['image_analysis_cache'].items():
            if isinstance(cached_data, dict):
                # 新格式：使用最新的文件路径作为key，分析结果作为value
                result[cached_data['file_path']] = cached_data['analysis']
            else:
                # 兼容旧格式
                result[cache_key] = cached_data
        return result
    
    def clear_image_analysis_cache(self, user_id: str, message_id: str):
        """清空图片分析缓存"""
        state = self.get_user_state(user_id, message_id)
        state['image_analysis_cache'].clear()
        print(f"🗑️ 清空图片分析缓存: {user_id}:{message_id}")

    def clear_system_messages_by_type(self, user_id: str, message_id: str, message_type: str):
        """清理特定类型的SystemMessage"""
        memory = self.get_memory(user_id, message_id)
        messages = memory.chat_memory.messages
        
        # 过滤掉包含特定关键词的SystemMessage
        filtered_messages = []
        removed_count = 0
        
        for msg in messages:
            if isinstance(msg, SystemMessage):
                # 根据消息内容判断类型并决定是否保留
                content = msg.content
                should_remove = False
                
                if message_type == "file_path" and "<file_path>" in content:
                    should_remove = True
                elif message_type == "image_path" and "图像保存路径" in content:
                    should_remove = True
                elif message_type == "image_style" and "绘图样式参考图" in content:
                    should_remove = True
                elif message_type == "latest_code" and ("上次生成的绘图代码" in content or "绘图代码（第" in content):
                    # 更全面地匹配绘图代码消息
                    should_remove = True
                
                if not should_remove:
                    filtered_messages.append(msg)
                else:
                    removed_count += 1
            else:
                filtered_messages.append(msg)
        
        # 重新设置消息列表
        memory.chat_memory.messages = filtered_messages
        
        if removed_count > 0:
            print(f"🧹 清理内存中的 {message_type} 类型消息: 移除了 {removed_count} 条")
    
    def add_or_update_system_message(self, user_id: str, message_id: str, message_type: str, content: str, file_ids: List[str] = None):
        """添加或更新SystemMessage，避免重复"""
        # 先清理内存中同类型的旧消息
        self.clear_system_messages_by_type(user_id, message_id, message_type)
        
        # 先清理数据库中同类型的旧消息
        self.db.delete_system_messages_by_type(user_id, message_id, message_type)
        
        # 添加新消息到内存
        memory = self.get_memory(user_id, message_id)
        if file_ids:
            memory.chat_memory.add_message(SystemMessage(content=content, file_ids=file_ids))
            # 保存到数据库
            self.save_message_to_db(user_id, message_id, "system", content, file_ids)
        else:
            memory.chat_memory.add_message(SystemMessage(content=content))
            # 保存到数据库
            self.save_message_to_db(user_id, message_id, "system", content)

    def update_latest_plotting_code(self, user_id: str, message_id: str, code: str):
        """更新用户最新的绘图代码"""
        state = self.get_user_state(user_id, message_id)
        state['latest_plotting_code'] = code
        state['code_generation_count'] += 1
        
        # 彻底清理所有旧的绘图代码消息，确保只保留最新的
        print(f"🔄 更新绘图代码 (第{state['code_generation_count']}次)，先清理旧代码消息...")
        self.clear_system_messages_by_type(user_id, message_id, "latest_code")
        self.db.delete_system_messages_by_type(user_id, message_id, "latest_code")
        
        # 将最新代码添加到系统消息中
        if code and code.strip():
            code_message = (
                f"📋 **上次生成的绘图代码（第{state['code_generation_count']}次）**：\n"
                "```python\n"
                f"{code}\n"
                "```\n"
                "💡 **优化建议**：如果用户要求修改图表，请在此代码基础上进行调整，而不是重新编写。"
                "保持代码的连续性和一致性，只修改需要变更的部分。"
            )
            
            # 直接添加到内存和数据库，不使用add_or_update_system_message避免重复清理
            memory = self.get_memory(user_id, message_id)
            memory.chat_memory.add_message(SystemMessage(content=code_message))
            self.save_message_to_db(user_id, message_id, "system", code_message)
            
            print(f"✅ 绘图代码已更新并添加到系统消息 (代码长度: {len(code)} 字符)")
    
    def get_latest_plotting_code(self, user_id: str, message_id: str) -> str:
        """获取用户最新的绘图代码"""
        state = self.get_user_state(user_id, message_id)
        return state.get('latest_plotting_code', '')
    
    def clear_latest_plotting_code(self, user_id: str, message_id: str):
        """清理用户的绘图代码（用于全新绘图任务）"""
        state = self.get_user_state(user_id, message_id)
        state['latest_plotting_code'] = None
        state['code_generation_count'] = 0
        # 清理内存中的代码消息
        self.clear_system_messages_by_type(user_id, message_id, "latest_code")
        # 清理数据库中的代码消息
        self.db.delete_system_messages_by_type(user_id, message_id, "latest_code") 