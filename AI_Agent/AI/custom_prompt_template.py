from langchain.prompts import StringPromptTemplate
from typing import List
from langchain_core.tools import BaseTool
import pandas as pd
import re
import os
import json
import xml.etree.ElementTree as ET
import PyPDF2
import docx

from utils import red_log

def safe_read_dataframe(path):
    ext = os.path.splitext(path)[1].lower()
    encodings = ["utf-8", "utf-8-sig", "gbk", "latin1"]

    if ext in [".csv", ".txt",".tsv"]:
        for enc in encodings:
            try:
                df = pd.read_csv(path, encoding=enc, on_bad_lines='skip')
                return df.head(3).to_markdown()
            except Exception as e:
                print(e)
                continue
        raise ValueError("文本文件编码错误，读取失败")

    # elif ext == ".tsv":
    #     for enc in encodings:
    #         try:
    #             df = pd.read_csv(path, sep='\t', encoding=enc, on_bad_lines='skip')
    #             return df.head(3).to_markdown()
    #         except Exception:
    #             continue
    #     raise ValueError("TSV 文件编码错误，读取失败")

    elif ext in [".xlsx", ".xls"]:
        df = pd.read_excel(path)
        return df.head(3).to_markdown()

    elif ext == ".json":
        with open(path, "r", encoding="utf-8") as f:
            data = json.load(f)
        return json.dumps(data, indent=2)[:2000]

    elif ext == ".xml":
        tree = ET.parse(path)
        root = tree.getroot()
        return ET.tostring(root, encoding='unicode')[:2000]

    elif ext == ".pdf":
        with open(path, "rb") as f:
            reader = PyPDF2.PdfReader(f)
            text = ""
            for page in reader.pages[:3]:
                text += page.extract_text()
        return text[:2000]

    elif ext == ".docx":
        doc_file = docx.Document(path)
        text = "\n".join([para.text for para in doc_file.paragraphs])
        return text[:2000]

    else:
        raise ValueError(f"暂不支持该文件类型：{ext}")

class CustomPromptTemplate(StringPromptTemplate):
    template: str
    tools: List[BaseTool]

    def format(self, **kwargs) -> str:
        intermediate_steps = kwargs.pop("intermediate_steps")
        thoughts = ""
        for action, observation in intermediate_steps:
            thoughts += action.log
            thoughts += f"\n观察: {observation}\n思考: "

        kwargs["agent_scratchpad"] = thoughts
        kwargs["tools"] = "\n".join([f"{tool.name}: {tool.description}" for tool in self.tools])
        kwargs["tool_names"] = ", ".join([tool.name for tool in self.tools])

        # 从history中提取绘图代码标记
        history_content = kwargs.get("history", "")
        print(history_content)
        plotting_code_match = re.search(r'<PLOTTING_CODE_MARKER>(.*?)</PLOTTING_CODE_MARKER>', history_content, re.DOTALL)
        print(plotting_code_match)
        if plotting_code_match:
            # 提取绘图代码内容
            kwargs["history_plotting_code"] = plotting_code_match.group(1).strip()
            # 从history中移除绘图代码标记
            kwargs["history"] = re.sub(r'<PLOTTING_CODE_MARKER>.*?</PLOTTING_CODE_MARKER>', '', history_content, flags=re.DOTALL).strip()
        else:
            # 如果没有找到标记，设置默认值
            kwargs["history_plotting_code"] = ""

        # match = re.search("<csv>'(.*?)'</csv>", kwargs["history"], re.DOTALL)
        # if match:
        #     file_path = match.group(1)
        #     try:
        #         kwargs["df_head"] = safe_read_dataframe(file_path)
        #     except Exception as e:
        #         kwargs["df_head"] = f"读取文件失败：{str(e)}"
        # else:
        #     kwargs["df_head"] = "目前不存在该信息"

        # return self.template.format(**kwargs)


        matches = re.findall(r"<file_path>'(.*?)'</file_path>", kwargs.get("history", ""), re.DOTALL)
        df_heads = []
        for i, file_path in enumerate(matches):
            ext = os.path.splitext(file_path)[1].lower()
            if ext in [".png", ".jpg", ".jpeg", ".gif", ".bmp"]:
                df_heads.append(f"文件{i+1}：{file_path}（图像文件，不用于读取摘要）")
                continue
            try:
                summary = safe_read_dataframe(file_path)
                df_heads.append(f"文件{i+1}：{file_path}\n{summary}")
            except Exception as e:
                df_heads.append(f"文件{i+1}：{file_path}（读取失败：{str(e)}）")
        kwargs["df_head"] = "\n\n".join(df_heads) if df_heads else "⚠️ 无法读取任何文件摘要。"
        
        full_prompt = self.template.format(**kwargs)

        print("\n [Prompt发送给大模型内容如下] ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓\n")
        print(full_prompt)
        print("\n [End of Prompt] ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑\n")
        red_log("准备发送Prompt给LLM")

        return full_prompt
        #return self.template.format(**kwargs)