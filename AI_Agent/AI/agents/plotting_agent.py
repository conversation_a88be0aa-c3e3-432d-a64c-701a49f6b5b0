"""
绘图Agent - 专门处理数据可视化和图表创建需求
"""

from typing import Dict, List, Optional, Any
from .base_agent import BaseAgent


class PlottingAgent(BaseAgent):
    """绘图Agent - 专门处理数据可视化、图表创建和图表修改需求"""
    
    def __init__(self, llm_model, tools, database=None):
        super().__init__(llm_model, tools, "PlottingAgent", "PLOTTING", database)
        self.database = database
    
    def get_max_iterations(self) -> int:
        """绘图Agent可能需要更多迭代来完善图表"""
        return 3
    
    def preprocess_input(self, user_input: str, context: Optional[Dict] = None) -> str:
        """预处理绘图输入"""
        # 直接返回用户输入，不再手动添加绘图代码
        # 绘图代码将通过系统消息在history中自动包含，避免重复
        print(f"📝 PlottingAgent 处理用户输入（依赖系统消息中的绘图代码）")
        return user_input
    
    def postprocess_result(self, result: Dict, context: Optional[Dict] = None) -> Dict[str, Any]:
        """后处理绘图结果"""
        processed_result = super().postprocess_result(result, context)
        
        # 为绘图结果添加额外信息
        processed_result['interaction_type'] = 'plotting'
        processed_result['chart_info'] = self._extract_chart_info(result.get('output', ''))
        processed_result['plotting_code'] = self._extract_plotting_code(result.get('intermediate_steps', []))
        
        return processed_result
    
    def _extract_chart_info(self, output: str) -> Dict[str, Any]:
        """从输出中提取图表信息"""
        info = {
            'chart_type': None,
            'chart_created': False,
            'modifications_made': []
        }
        
        output_lower = output.lower()
        
        # 检测图表类型
        chart_types = {
            'bar': ['柱状图', 'bar', 'barplot'],
            'line': ['折线图', 'line', 'lineplot'],
            'scatter': ['散点图', 'scatter', 'scatterplot'],
            'heatmap': ['热力图', 'heatmap'],
            'box': ['箱线图', 'box', 'boxplot'],
            'histogram': ['直方图', 'hist', 'histogram']
        }
        
        for chart_type, keywords in chart_types.items():
            if any(keyword in output_lower for keyword in keywords):
                info['chart_type'] = chart_type
                break
        
        # 检测是否成功创建图表
        if '✅ 绘图已完成' in output or '绘图已完成' in output:
            info['chart_created'] = True
        
        # 检测修改类型
        modifications = {
            'color': ['颜色', 'color'],
            'title': ['标题', 'title'],
            'legend': ['图例', 'legend'],
            'label': ['标签', 'label'],
            'size': ['大小', 'size'],
            'position': ['位置', 'position']
        }
        
        for mod_type, keywords in modifications.items():
            if any(keyword in output_lower for keyword in keywords):
                info['modifications_made'].append(mod_type)
        
        return info
    
    def _extract_plotting_code(self, intermediate_steps: List) -> Optional[str]:
        """从中间步骤中提取绘图代码"""
        if not intermediate_steps:
            return None
        
        # 查找最后一次python绘图工具的使用
        for step in reversed(intermediate_steps):
            if isinstance(step, tuple) and len(step) >= 2:
                action = step[0]
                if hasattr(action, 'tool') and 'python' in action.tool.lower():
                    return action.tool_input
        
        return None 