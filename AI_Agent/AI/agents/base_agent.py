"""
基础Agent类 - 定义所有Agent的共同接口和基础功能
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from langchain.agents import AgentExecutor, LLMSingleActionAgent
from langchain.chains.llm import <PERSON><PERSON>hain
from langchain.memory import ConversationBufferMemory
from langchain_openai import ChatOpenAI

from ..custom_prompt_template import CustomPromptTemplate
from ..custom_output_parser import CustomOutputParser
from ..prompt_templates import prompt_assembler


class BaseAgent(ABC):
    """基础Agent抽象类"""
    
    def __init__(self, 
                 llm_model: ChatOpenAI,
                 tools: List,
                 agent_name: str = "BaseAgent",
                 intent_type: str = "QUESTION",
                 database=None):
        """
        初始化基础Agent
        
        Args:
            llm_model: 语言模型实例
            tools: 可用工具列表
            agent_name: Agent名称
            intent_type: 意图类型，用于选择对应的prompt模板
            database: 数据库实例，用于获取历史数据
        """
        self.llm_model = llm_model
        self.tools = tools
        self.tool_names = [tool.name for tool in tools]
        self.agent_name = agent_name
        self.intent_type = intent_type
        self.database = database
        self.output_parser = CustomOutputParser()
        
        # 初始化Agent
        self._setup_agent()
    
    def _setup_agent(self):
        """设置Agent执行器"""
        # 使用动态prompt模板系统
        prompt_template = prompt_assembler.get_template(self.intent_type)
        
        # 创建prompt实例
        self.prompt = CustomPromptTemplate(
            template=prompt_template,
            tools=self.tools,
            input_variables=["input", "intermediate_steps", "history"],
        )
        
        # 创建LLM链
        self.llm_chain = LLMChain(llm=self.llm_model, prompt=self.prompt)
        
        # 创建Agent
        self.agent = LLMSingleActionAgent(
            llm_chain=self.llm_chain,
            output_parser=self.output_parser,
            stop=["\n观察：", "\n观察:"],
            allowed_tools=self.tool_names
        )
        
        # 创建Agent执行器（暂时不设置内存，等待外部设置）
        self.agent_executor = AgentExecutor(
            agent=self.agent,
            tools=self.tools,
            memory=None,  # 将在process_request中动态设置
            verbose=True,
            max_iterations=self.get_max_iterations(),
            handle_parsing_errors=True,
            return_intermediate_steps=True
        )
        
        print(f"✅ {self.agent_name} 初始化完成")
    
    @abstractmethod 
    def get_max_iterations(self) -> int:
        """
        获取最大迭代次数 - 子类必须实现
        
        Returns:
            int: 最大迭代次数
        """
        pass
    
    def process_request(self, 
                       user_input: str,
                       context: Optional[Dict] = None,
                       memory=None) -> Dict[str, Any]:
        """
        处理用户请求
        
        Args:
            user_input: 用户输入
            context: 上下文信息
            memory: 外部内存实例
            
        Returns:
            Dict: 处理结果
        """
        try:
            print(f"🤖 {self.agent_name} 开始处理请求...")
            
            # 设置内存（如果提供了外部内存）
            if memory is not None:
                self.agent_executor.memory = memory
            elif self.agent_executor.memory is None:
                # 如果没有内存，创建一个新的
                self.agent_executor.memory = ConversationBufferMemory()
            
            # 预处理输入
            processed_input = self.preprocess_input(user_input, context)
            
            # 准备执行参数
            execution_params = {
                "input": processed_input
            }
            
            # 构建对话历史 - 优先从memory中获取，其次从context获取
            history_content = ""
            if self.agent_executor.memory is not None:
                # 从memory中构建history字符串
                messages = self.agent_executor.memory.chat_memory.messages
                history_parts = []
                for msg in messages:
                    if hasattr(msg, 'content'):
                        if msg.__class__.__name__ == 'HumanMessage':
                            history_parts.append(f"Human: {msg.content}")
                        elif msg.__class__.__name__ == 'AIMessage':
                            history_parts.append(f"Assistant: {msg.content}")
                        elif msg.__class__.__name__ == 'SystemMessage' and "上次生成的绘图代码（第" not in msg.content:
                            history_parts.append(f"System: {msg.content}")
                history_content = "\n".join(history_parts)
                print(f"📖 从memory构建对话历史，包含 {len(messages)} 条消息")
            elif context and context.get("history"):
                # 回退到context中的history
                history_content = context.get("history", "")
                print(f"📖 从context获取对话历史")
            
            execution_params["history"] = history_content
            
            # 从数据库获取本会话的绘图代码，并添加到history中
            plotting_code_content = ""
            
            if self.database is not None:
                try:
                    user_id = context.get("user_id") if context else None
                    message_id = context.get("message_id") if context else None
                    
                    if user_id and message_id:
                        plotting_code = self.database.get_latest_plotting_code(user_id, message_id)
                        if plotting_code:
                            plotting_code_content = plotting_code
                            print(f"📖 从数据库获取到本会话({user_id}: {message_id})历史绘图代码")
                    else:
                        print(f"⚠️ 缺少user_id或message_id，无法获取历史绘图代码")
                except Exception as e:
                    print(f"⚠️ 获取本会话历史绘图代码失败: {str(e)}")
            else:
                print(f"⚠️ 数据库未初始化，无法获取历史绘图代码")
            
            # 将绘图代码以特殊标记添加到history末尾，供CustomPromptTemplate提取
            if plotting_code_content:
                execution_params["history"] += f"\n<PLOTTING_CODE_MARKER>{plotting_code_content}</PLOTTING_CODE_MARKER>"
            else:
                execution_params["history"] += f"\n<PLOTTING_CODE_MARKER>没有历史绘图代码</PLOTTING_CODE_MARKER>"
            print(execution_params)
            # 执行Agent
            result = self.agent_executor.invoke(execution_params)
            
            # 后处理结果
            processed_result = self.postprocess_result(result, context)
            
            print(f"✅ {self.agent_name} 处理完成")
            return processed_result
            
        except Exception as e:
            print(f"❌ {self.agent_name} 处理失败: {str(e)}")
            return {
                'output': f"抱歉，{self.agent_name} 处理您的请求时发生了错误：{str(e)}",
                'error': str(e),
                'agent_name': self.agent_name
            }
    
    def preprocess_input(self, user_input: str, context: Optional[Dict] = None) -> str:
        """
        预处理用户输入 - 子类可重写
        
        Args:
            user_input: 原始用户输入
            context: 上下文信息
            
        Returns:
            str: 处理后的输入
        """
        return user_input
    
    def postprocess_result(self, result: Dict, context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        后处理Agent结果 - 子类可重写
        
        Args:
            result: Agent执行结果
            context: 上下文信息
            
        Returns:
            Dict: 处理后的结果
        """
        return {
            'output': result.get('output', ''),
            'intermediate_steps': result.get('intermediate_steps', []),
            'agent_name': self.agent_name
        }
    
    def get_agent_info(self) -> Dict[str, Any]:
        """
        获取Agent信息
        
        Returns:
            Dict: Agent信息
        """
        return {
            'name': self.agent_name,
            'tools': self.tool_names,
            'max_iterations': self.get_max_iterations()
        } 