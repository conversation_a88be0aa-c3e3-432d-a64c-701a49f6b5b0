"""A tool for running python code in a REPL."""

import ast
import re
import sys
from contextlib import redirect_stdout
from io import StringIO
from typing import Any, Dict, Optional, Type

from langchain_core.callbacks.manager import (
    AsyncCallbackManagerForToolRun,
    CallbackManagerForToolRun,
)
from langchain_core.runnables.config import run_in_executor
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field, model_validator

from langchain_experimental.utilities.python import PythonREPL


def _get_default_python_repl() -> PythonREPL:
    return PythonREPL(_globals=globals(), _locals=None)


def sanitize_input(query: str) -> str:
    """Sanitize input to the python REPL.

    Remove whitespace, backtick & python (if llm mistakes python console as terminal)

    Args:
        query: The query to sanitize

    Returns:
        str: The sanitized query
    """

    # Removes `, whitespace & python from start
    query = re.sub(r"^(\s|`)*(?i:python)?\s*", "", query)
    # Removes whitespace & ` from end
    query = re.sub(r"(\s|`)*$", "", query)
    return query


def check_code_security(code: str, llm_model=None) -> tuple[bool, str]:
    """
    使用 LLM 检查 Python 代码的安全性
    
    Args:
        code: 要检查的 Python 代码
        llm_model: LLM 模型实例，如果为 None 则跳过检查
        
    Returns:
        tuple: (is_safe: bool, reason: str) - 是否安全及原因说明
    """
    if llm_model is None:
        # 如果没有提供 LLM 模型，使用基础规则检查
        return _basic_security_check(code)
    
    security_prompt = f"""
你是一个Python代码安全分析专家。请分析以下Python代码是否存在安全风险。

需要重点检查的安全问题包括：
1. 文件系统操作：删除、修改重要文件或系统文件
2. 网络操作：未授权的网络访问、下载恶意文件
3. 系统命令执行：使用os.system、subprocess等执行系统命令
4. 环境变量操作：修改重要的环境变量
5. 包管理操作：安装、卸载Python包
6. 权限提升：尝试获取更高权限
7. 无限循环：可能导致系统资源耗尽
8. 恶意导入：导入潜在危险的模块

代码：
```python
{code}
```

请严格按照以下格式回复：
安全性：[安全/危险]
原因：[具体的安全分析]

注意：数据分析、可视化、数学计算等正常操作应判断为安全。
"""

    try:
        response = llm_model.invoke(security_prompt)
        response_text = response.content if hasattr(response, 'content') else str(response)
        
        # 解析 LLM 响应
        if "安全性：安全" in response_text or "安全性: 安全" in response_text:
            return True, "LLM 安全检查通过"
        elif "安全性：危险" in response_text or "安全性: 危险" in response_text:
            # 提取原因
            reason_match = re.search(r"原因[：:]\s*(.+)", response_text, re.DOTALL)
            reason = reason_match.group(1).strip() if reason_match else "检测到潜在安全风险"
            return False, f"LLM 安全检查失败: {reason}"
        else:
            # 如果 LLM 响应格式异常，使用基础检查
            return _basic_security_check(code)
            
    except Exception as e:
        # LLM 检查失败时，回退到基础安全检查
        print(f"LLM 安全检查异常: {e}")
        return _basic_security_check(code)


def check_analysis_code_security(code: str, llm_model=None) -> tuple[bool, str]:
    """
    专门用于数据分析的代码安全检查（禁用绘图功能）
    
    Args:
        code: 要检查的 Python 代码
        llm_model: LLM 模型实例
        
    Returns:
        tuple: (is_safe: bool, reason: str) - 是否安全及原因说明
    """
    # 首先检查是否包含绘图相关代码
    plotting_patterns = [
        r'\bmatplotlib\b', r'\bplt\.\b', r'\bseaborn\b', r'\bsns\.\b',
        r'\bplot\(', r'\bplotly\b', r'\bfigure\(', r'\bsubplot',
        r'\.plot\(', r'savefig\(', r'show\(', r'\bbox\b',
        r'scatter\(', r'bar\(', r'hist\(', r'line\('
    ]
    
    code_lower = code.lower()
    for pattern in plotting_patterns:
        if re.search(pattern, code_lower):
            return False, f"检测到绘图相关代码：{pattern}。数据分析工具不支持绘图功能，请使用专门的绘图Agent。"
    
    # 然后进行常规安全检查
    return check_code_security(code, llm_model)


def _basic_security_check(code: str) -> tuple[bool, str]:
    """
    基础的代码安全检查（不依赖LLM）
    
    Args:
        code: 要检查的 Python 代码
        
    Returns:
        tuple: (is_safe: bool, reason: str)
    """
    dangerous_patterns = [
        # 文件系统危险操作
        (r'\bos\.remove\b|\bos\.unlink\b|\bos\.rmdir\b', "检测到文件删除操作"),
        (r'\bshutil\.rmtree\b', "检测到目录删除操作"),
        (r'\bos\.system\b', "检测到系统命令执行"),
        (r'\bsubprocess\.(call|run|Popen)\b', "检测到子进程执行"),
        
        # 网络操作
        (r'\burllib\.request\b|\brequests\.(get|post|put|delete)\b', "检测到网络请求操作"),
        
        # 包管理
        (r'\bpip\.main\b|\b__import__\(\s*["\']pip["\']\s*\)', "检测到pip包管理操作"),
        
        # 环境变量
        (r'\bos\.environ\[.*\]\s*=', "检测到环境变量修改"),
        
        # 危险导入
        (r'\bimport\s+(os|subprocess|sys)\b', "检测到潜在危险模块导入"),
    ]
    
    for pattern, message in dangerous_patterns:
        if re.search(pattern, code, re.IGNORECASE):
            return False, message
    
    # 检查无限循环（简单检查）
    if re.search(r'\bwhile\s+True\s*:', code) and 'break' not in code:
        return False, "检测到可能的无限循环"
    
    return True, "基础安全检查通过"


class PythonAnalysisTool(BaseTool):
    """专门用于数据分析的Python工具（不支持绘图功能）"""

    name: str = "python数据分析"
    description: str = (
        "专业的Python数据分析工具。适用于：\n"
        "1. 数据处理：使用pandas进行数据清洗、筛选、变换、合并\n"
        "2. 统计分析：描述性统计、假设检验、相关性分析\n"
        "3. 数据计算：数学运算、特征工程、数据汇总\n"
        "4. 数据探索：数据形状、缺失值检查、数据类型分析\n"
        "注意：此工具专注于数据分析，不支持绘图功能。如需绘图请使用专门的绘图Agent\n"
    )
    python_repl: PythonREPL = Field(default_factory=_get_default_python_repl)
    sanitize_input: bool = True
    security_llm: Optional[Any] = Field(default=None, description="LLM model for security checking")
    enable_security_check: bool = Field(default=True, description="Whether to enable security checking")

    def _run(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> Any:
        """Use the tool."""
        if self.sanitize_input:
            query = sanitize_input(query)
        
        # 数据分析专用安全检查（包含绘图检查）
        if self.enable_security_check:
            print(f"\n🔒 开始数据分析代码安全检查...")
            is_safe, reason = check_analysis_code_security(query, self.security_llm)
            if not is_safe:
                return f"🚫 代码安全检查失败: {reason}\n\n请修改您的代码，确保只进行数据分析操作。"
            else:
                print(f"\n🔒 数据分析代码安全检查通过: {reason}")
        
        return self.python_repl.run(query)

    async def _arun(
        self,
        query: str,
        run_manager: Optional[AsyncCallbackManagerForToolRun] = None,
    ) -> Any:
        """Use the tool asynchronously."""
        if self.sanitize_input:
            query = sanitize_input(query)

        # 数据分析专用安全检查
        if self.enable_security_check:
            is_safe, reason = check_analysis_code_security(query, self.security_llm)
            if not is_safe:
                return f"🚫 代码安全检查失败: {reason}\n\n请修改您的代码，确保只进行数据分析操作。"

        return await run_in_executor(None, self.python_repl.run, query)


class PythonPlottingTool(BaseTool):
    """专门用于绘图和可视化的Python工具"""

    name: str = "python绘图"
    description: str = (
        "强大的Python数据可视化工具。适用于：\n"
        "1. 数据可视化：使用matplotlib/seaborn创建各类图表\n"
        "2. 图表类型：柱状图、折线图、散点图、热图、饼图、箱线图等\n"
        "3. 图表美化：样式定制、颜色配置、标题标签设置\n"
        "4. 数据处理：基础的数据读取和处理以支持绘图\n"
        "注意：请确保所有绘图都保存到指定路径，不要使用plt.show()显示图片\n"
    )
    python_repl: PythonREPL = Field(default_factory=_get_default_python_repl)
    sanitize_input: bool = True
    security_llm: Optional[Any] = Field(default=None, description="LLM model for security checking")
    enable_security_check: bool = Field(default=True, description="Whether to enable security checking")

    def _run(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> Any:
        """Use the tool."""
        if self.sanitize_input:
            query = sanitize_input(query)
        
        # 绘图工具使用标准安全检查
        if self.enable_security_check:
            print(f"\n🔒 开始绘图代码安全检查...")
            is_safe, reason = check_code_security(query, self.security_llm)
            if not is_safe:
                return f"🚫 代码安全检查失败: {reason}\n\n请修改您的代码以确保安全性。"
            else:
                print(f"\n🔒 绘图代码安全检查通过: {reason}")
        
        return self.python_repl.run(query)

    async def _arun(
        self,
        query: str,
        run_manager: Optional[AsyncCallbackManagerForToolRun] = None,
    ) -> Any:
        """Use the tool asynchronously."""
        if self.sanitize_input:
            query = sanitize_input(query)

        # 绘图工具安全检查
        if self.enable_security_check:
            is_safe, reason = check_code_security(query, self.security_llm)
            if not is_safe:
                return f"🚫 代码安全检查失败: {reason}\n\n请修改您的代码以确保安全性。"

        return await run_in_executor(None, self.python_repl.run, query)


# 保持向后兼容性的别名
PythonREPLTool = PythonPlottingTool


class PythonInputs(BaseModel):
    """Python inputs."""

    query: str = Field(description="code snippet to run")


class PythonAstREPLTool(BaseTool):
    """Tool for running python code in a REPL."""

    name: str = "python_repl_ast"
    description: str = (
        "A Python shell. Use this to execute python commands. "
        "Input should be a valid python command. "
        "When using this tool, sometimes output is abbreviated - "
        "make sure it does not look abbreviated before using it in your answer."
    )
    globals: Optional[Dict] = Field(default_factory=dict)
    locals: Optional[Dict] = Field(default_factory=dict)
    sanitize_input: bool = True
    args_schema: Type[BaseModel] = PythonInputs

    @model_validator(mode="before")
    @classmethod
    def validate_python_version(cls, values: Dict) -> Any:
        """Validate valid python version."""
        if sys.version_info < (3, 9):
            raise ValueError(
                "This tool relies on Python 3.9 or higher "
                "(as it uses new functionality in the `ast` module, "
                f"you have Python version: {sys.version}"
            )
        return values

    def _run(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Use the tool."""
        try:
            if self.sanitize_input:
                query = sanitize_input(query)
            tree = ast.parse(query)
            module = ast.Module(tree.body[:-1], type_ignores=[])
            exec(ast.unparse(module), self.globals, self.locals)  # type: ignore
            module_end = ast.Module(tree.body[-1:], type_ignores=[])
            module_end_str = ast.unparse(module_end)  # type: ignore
            io_buffer = StringIO()
            try:
                with redirect_stdout(io_buffer):
                    ret = eval(module_end_str, self.globals, self.locals)
                    if ret is None:
                        return io_buffer.getvalue()
                    else:
                        return ret
            except Exception:
                with redirect_stdout(io_buffer):
                    exec(module_end_str, self.globals, self.locals)
                return io_buffer.getvalue()
        except Exception as e:
            return "{}: {}".format(type(e).__name__, str(e))

    async def _arun(
        self,
        query: str,
        run_manager: Optional[AsyncCallbackManagerForToolRun] = None,
    ) -> Any:
        """Use the tool asynchronously."""

        return await run_in_executor(None, self._run, query)
