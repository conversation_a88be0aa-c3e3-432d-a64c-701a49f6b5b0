"""
API路由模块 - 包含所有FastAPI路由处理函数
"""
import os
from typing import List
from fastapi import FastAPI, UploadFile, File, Form
from fastapi.responses import StreamingResponse

from models import ChatMessage
from memory_manager import UserMemoryManager
from utils import get_image_path
from file_handler import <PERSON><PERSON>and<PERSON>
from chat_handler import <PERSON>t<PERSON><PERSON><PERSON>

from AI.agent import model, plotting_tools, analysis_tools, chat_tools  # 引入基础组件
from AI.agent_manager import AgentManager  # 引入新的Agent管理器

# 创建全局内存管理器实例
memory_manager = UserMemoryManager()

# 创建全局Agent管理器实例
agent_manager = AgentManager(model, plotting_tools, analysis_tools, chat_tools, memory_manager.db)

# 创建处理器实例
file_handler = FileHandler(memory_manager)
chat_handler = <PERSON><PERSON><PERSON><PERSON><PERSON>(memory_manager, agent_manager)


def setup_routes(app: FastAPI):
    """设置所有API路由"""
    
    @app.get("/conversations/{user_id}")
    async def get_user_conversations(user_id: str):
        """获取用户的所有对话列表"""
        try:
            conversations = memory_manager.db.get_user_conversations_with_details(user_id)
            return {"success": True, "conversations": conversations}
        except Exception as e:
            return {"success": False, "error": str(e)}

    @app.get("/conversations/{user_id}/{message_id}/messages")
    async def get_conversation_messages(user_id: str, message_id: str):
        """获取指定对话的所有消息（不包含系统消息）"""
        try:
            messages = memory_manager.db.load_messages_for_api(user_id, message_id)
            return {"success": True, "messages": messages}
        except Exception as e:
            return {"success": False, "error": str(e)}

    @app.delete("/conversations/{user_id}/{message_id}")
    async def delete_conversation(user_id: str, message_id: str):
        """删除指定的对话"""
        try:
            result = memory_manager.db.delete_conversation(user_id, message_id)
            if result:
                # 同时清理内存中的数据
                memory_key = f"{user_id}:{message_id}"
                with memory_manager.lock:
                    memory_manager._remove_cache_entry(memory_key)
                print(f"🗑️ 删除对话及缓存: {memory_key}")
                return {"success": True, "message": "对话删除成功"}
            else:
                return {"success": False, "error": "对话不存在或删除失败"}
        except Exception as e:
            return {"success": False, "error": str(e)}

    @app.get("/admin/cache-stats")
    async def get_cache_stats():
        """获取内存缓存统计信息（管理员接口）"""
        try:
            stats = memory_manager.get_cache_stats()
            return {"success": True, "stats": stats}
        except Exception as e:
            return {"success": False, "error": str(e)}

    @app.post("/chat")
    async def chat_stream(
        chat_data: str = Form(...),
        files: List[UploadFile] = File(None),
        user_id: str = Form(...),
        message_id: str = Form(...)
    ):
        """主要的聊天流处理函数"""
        # 获取当前用户的对话记忆和状态
        user_memory = memory_manager.get_memory(user_id, message_id)
        user_state = memory_manager.get_user_state(user_id, message_id)

        message = ChatMessage.model_validate_json(chat_data)
        
        # 处理重置命令
        should_reset_code = chat_handler.handle_reset_commands(message, user_id, message_id)
        
        # 处理绘图代码上下文
        chat_handler.handle_code_context(should_reset_code, user_id, message_id, user_state)

        # 处理文件上传
        file_infos = await file_handler.process_uploaded_files(files, user_id, message_id)
        
        # 检查文件处理中的错误
        for info in file_infos:
            if info["code"] == 2:
                return StreamingResponse(
                    iter([info["message"]]),
                    media_type="text/event-stream"
                )
        
        # 处理文件分析
        valid_paths, warnings, newly_analyzed_images = file_handler.process_file_analysis(file_infos, user_id, message_id)
        
        # 检查数据验证警告
        if warnings:
            warning_text = "\n\n".join(warnings)
            return StreamingResponse(
                iter([f"⚠️ 数据检查未通过：\n{warning_text}"]),
                media_type="text/event-stream"
            )
        
        # 生成新的图像路径（每次请求都生成，保证时间戳唯一性）
        image_path = get_image_path()
        memory_manager.update_image_path(user_id, message_id, image_path)
        
        # 处理文件路径和图像样式参考
        file_handler.handle_file_paths_and_styles(valid_paths, user_id, message_id, image_path, newly_analyzed_images, user_state)
        
        # 🚀 使用Agent管理器处理请求
        has_files = bool(valid_paths)
        context_history = chat_handler.get_context_history(user_id, message_id)
        previous_intent = user_state.get('last_intent', None)
        
        # 准备Agent管理器的上下文
        agent_context = {
            'df_head': chat_handler.get_dataframe_preview(valid_paths),  # 获取数据预览
            'has_files': has_files,
            'image_path': image_path,
            'user_state': user_state,
            'user_id': user_id,  # 添加user_id和message_id供Agent管理器使用
            'message_id': message_id
        }
        
        # 使用Agent管理器路由请求
        session_id = f"{user_id}:{message_id}"
        
        return StreamingResponse(
            chat_handler.generate_agent_manager_response(message, session_id, has_files, agent_context), 
            media_type="text/event-stream"
        ) 